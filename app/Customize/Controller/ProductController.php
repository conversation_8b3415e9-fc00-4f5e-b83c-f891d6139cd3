<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Customize\Controller;

use Carbon\Carbon;
use Eccube\Entity\Tag;
use Eccube\Entity\Order;
use Eccube\Entity\Product;
use Eccube\Common\Constant;
use Eccube\Entity\BaseInfo;
use Eccube\Entity\Category;
use Eccube\Event\EventArgs;
use Eccube\Entity\OrderItem;
use Eccube\Entity\ProductTag;
use Eccube\Event\EccubeEvents;
use Eccube\Service\CartService;
use Eccube\Form\Type\AddCartType;
use Eccube\Entity\ProductCategory;
use Eccube\Repository\TagRepository;
use Eccube\Entity\Master\OrderStatus;
use Eccube\Entity\Master\ProductStatus;
use Eccube\Repository\PluginRepository;
use Eccube\Repository\ProductRepository;
use Eccube\Controller\AbstractController;
use Eccube\Repository\BaseInfoRepository;
use Customize\Form\Type\SearchProductType;
use Knp\Component\Pager\PaginatorInterface;
use Eccube\Service\PurchaseFlow\PurchaseFlow;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Eccube\Service\PurchaseFlow\PurchaseContext;
use Eccube\Repository\Master\ProductListMaxRepository;
use Eccube\Repository\CustomerFavoriteProductRepository;
use Knp\Bundle\PaginatorBundle\Pagination\SlidingPagination;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Customize\Repository\DiscountGroupRepository;

class ProductController extends AbstractController
{
    /**
     * @var PurchaseFlow
     */
    protected $purchaseFlow;

    /**
     * @var CustomerFavoriteProductRepository
     */
    protected $customerFavoriteProductRepository;

    /**
     * @var CartService
     */
    protected $cartService;

    /**
     * @var ProductRepository
     */
    protected $productRepository;

    /**
     * @var BaseInfo
     */
    protected $BaseInfo;

    /**
     * @var AuthenticationUtils
     */
    protected $helper;

    /**
     * @var ProductListMaxRepository
     */
    protected $productListMaxRepository;

    private $title = '';

    /**
     * @var PluginRepository
     */
    protected $pluginRepository;

    /**
     * @var DiscountGroupRepository
     */
    private $discountGroupRepository;

    /**
     * ProductController constructor.
     *
     * @param PurchaseFlow $cartPurchaseFlow
     * @param CustomerFavoriteProductRepository $customerFavoriteProductRepository
     * @param CartService $cartService
     * @param ProductRepository $productRepository
     * @param BaseInfoRepository $baseInfoRepository
     * @param AuthenticationUtils $helper
     * @param ProductListMaxRepository $productListMaxRepository
     * @param PluginRepository $pluginRepository
     */
    public function __construct(
        PurchaseFlow $cartPurchaseFlow,
        CustomerFavoriteProductRepository $customerFavoriteProductRepository,
        CartService $cartService,
        ProductRepository $productRepository,
        BaseInfoRepository $baseInfoRepository,
        AuthenticationUtils $helper,
        ProductListMaxRepository $productListMaxRepository,
        PluginRepository $pluginRepository,
        DiscountGroupRepository $discountGroupRepository,
    ) {
        $this->purchaseFlow = $cartPurchaseFlow;
        $this->customerFavoriteProductRepository = $customerFavoriteProductRepository;
        $this->cartService = $cartService;
        $this->productRepository = $productRepository;
        $this->BaseInfo = $baseInfoRepository->get();
        $this->helper = $helper;
        $this->productListMaxRepository = $productListMaxRepository;
        $this->pluginRepository = $pluginRepository;
        $this->discountGroupRepository = $discountGroupRepository;
    }

    /**
     * 商品一覧画面.
     *
     * @Route("/products/list", name="product_list", methods={"GET"})
     * @Template("Product/list.twig")
     */
    public function index(Request $request, PaginatorInterface $paginator)
    {
        $pageLoading = $request->query->get('pageLoading', 1);

        // Doctrine SQLFilter
        if ($this->BaseInfo->isOptionNostockHidden()) {
            $this->entityManager->getFilters()->enable('option_nostock_hidden');
        }

        // handleRequestは空のqueryの場合は無視するため
        if ($request->getMethod() === 'GET') {
            $request->query->set('pageno', $request->query->get('pageno', ''));
        }

        // searchForm
        /* @var $builder \Symfony\Component\Form\FormBuilderInterface */
        $builder = $this->formFactory->createNamedBuilder('', SearchProductType::class);

        if ($request->getMethod() === 'GET') {
            $builder->setMethod('GET');
        }

        $event = new EventArgs(
            [
                'builder' => $builder,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_INDEX_INITIALIZE);

        /* @var $searchForm \Symfony\Component\Form\FormInterface */
        $searchForm = $builder->getForm();

        $searchForm->handleRequest($request);

        // paginator
        $searchData = $searchForm->getData();
        $qb = $this->productRepository->getQueryBuilderBySearchData($searchData);

        $event = new EventArgs(
            [
                'searchData' => $searchData,
                'qb' => $qb,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_INDEX_SEARCH);
        $searchData = $event->getArgument('searchData');

        $query = $qb->getQuery()
            ->useResultCache(true, $this->eccubeConfig['eccube_result_cache_lifetime_short']);

        /** @var SlidingPagination $pagination */
        $pagination = $paginator->paginate(
            $query,
            !empty($searchData['pageno']) ? (($searchData['pageno'] - 1) * 4 + $pageLoading) : $pageLoading,
            !empty($searchData['disp_number']) ? $searchData['disp_number']->getId() : $this->productListMaxRepository->findOneBy([], ['sort_no' => 'ASC'])->getId()
        );

        $ids = [];
        foreach ($pagination as $Product) {
            $ids[] = $Product->getId();
        }
        $ProductsAndClassCategories = $this->productRepository->findProductsWithSortedClassCategories($ids, 'p.id');

        // addCart form
        $forms = [];
        foreach ($pagination as $Product) {
            /* @var $builder \Symfony\Component\Form\FormBuilderInterface */
            $builder = $this->formFactory->createNamedBuilder(
                '',
                AddCartType::class,
                null,
                [
                    'product' => $ProductsAndClassCategories[$Product->getId()],
                    'allow_extra_fields' => true,
                ]
            );
            $addCartForm = $builder->getForm();

            $forms[$Product->getId()] = $addCartForm->createView();
        }

        $Category = $searchForm->get('category_id')->getData();

        // Check recommend plugin
        $hasRecommendPlugin = $this->pluginRepository->findByCode('Recommend42')->isEnabled();
        if ($request->isXmlHttpRequest()) {
            if (!empty($pagination->getItems())) {
                return new Response($this->renderView('Product/_part_product_list.twig', [
                    'pagination' => $pagination,
                ]));
            }

            return $this->json([
                'status_page_loading' => 'error',
            ]);
        }

        // Pagination
        $groupSize = 4;
        $paginationData = $pagination->getPaginationData();
        $current = ceil($paginationData['current'] / $groupSize);
        $pageCount = ceil($paginationData['pageCount'] / $groupSize);

        $pagesInRange = array_values(array_unique(array_filter(range($current - 2, $current + 2), fn($page) => $page > 0 && $page <= $pageCount)));
        $pagesInRange = array_merge($pagesInRange, range(1, $pageCount));
        $pagesInRange = array_slice(array_unique($pagesInRange), 0, 5);

        sort($pagesInRange);
        $groupedPages = compact('current', 'pageCount', 'pagesInRange');

        if (isset($paginationData['previous'])) {
            $groupedPages['previous'] = $current - 1;
        }

        if (isset($paginationData['next']) && $current < $pageCount) {
            $groupedPages['next'] = $current + 1;
        }

        return [
            'pagination' => $pagination,
            'groupedPages' => $groupedPages,
            'search_form' => $searchForm->createView(),
            'forms' => $forms,
            'Category' => $Category,
            'has_recommend_plugin' => $hasRecommendPlugin,
            'Request' => $request,
        ];
    }

    /**
     * 商品詳細画面.
     *
     * @Route("/products/detail/{id}", name="product_detail", methods={"GET"}, requirements={"id" = "\d+"})
     * @Template("Product/detail.twig")
     * @ParamConverter("Product", options={"repository_method" = "findWithSortedClassCategories"})
     *
     * @param Request $request
     * @param Product $Product
     *
     * @return array
     */
    public function detail(Request $request, Product $Product)
    {
        if (!$this->checkVisibility($Product)) {
            throw new NotFoundHttpException();
        }

        $builder = $this->formFactory->createNamedBuilder(
            '',
            AddCartType::class,
            null,
            [
                'product' => $Product,
                'id_add_product_id' => false,
            ]
        );

        $event = new EventArgs(
            [
                'builder' => $builder,
                'Product' => $Product,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_DETAIL_INITIALIZE);

        $is_favorite = false;
        if ($this->isGranted('ROLE_USER')) {
            $Customer = $this->getUser();
            $is_favorite = $this->customerFavoriteProductRepository->isFavorite($Customer, $Product);
        }

        // Check recommend plugin
        $hasRecommendPlugin = $this->pluginRepository->findByCode('Recommend42')->isEnabled();

        // Check product review plugin
        $hasProductReviewPlugin = $this->pluginRepository->findByCode('ProductReview42')->isEnabled();

        $spec = [];
        if (!empty($Product['spec'])) {
            $array_explode = explode(PHP_EOL,$Product['spec']);
            foreach ($array_explode as $value) {
                $spec[] = explode(':',$value,2);
            }
        }

        $Gifts = $this->productRepository->getGiftOptions($Product['gift_type']);

        if (!$Product->getPrice01IncTaxMin()) {
            $tagIds = $this->discountGroupRepository->getTagForProduct($Product->getId());
            foreach ($tagIds as $tagData) {
                if (!$tagData['tag_id']) {
                    continue;
                }

                $Tag = $this->entityManager->getRepository(Tag::class)->find($tagData['tag_id']);

                if ($Tag) {
                    $existingTag = $Product->getProductTag()->filter(function($productTag) use ($Tag) {
                        return $productTag->getTag()->getId() === $Tag->getId();
                    })->first();

                    if (!$existingTag) {
                        $ProductTag = new ProductTag();
                        $ProductTag->setProduct($Product)->setTag($Tag);
                        $Product->addProductTag($ProductTag);
                    }
                }
            }

        }

        return [
            'title' => $this->title,
            'subtitle' => $Product->getName(),
            'form' => $builder->getForm()->createView(),
            'Product' => $Product,
            'is_favorite' => $is_favorite,
            'has_recommend_plugin' => $hasRecommendPlugin,
            'has_product_review_plugin' => $hasProductReviewPlugin,
            'Request' => $request,
            'spec' => $spec,
            'gifts' => $Gifts
        ];
    }

    /**
     * お気に入り追加.
     *
     * @Route("/products/add_favorite/{id}", name="product_add_favorite", requirements={"id" = "\d+"}, methods={"GET", "POST"})
     */
    public function addFavorite(Request $request, Product $Product)
    {
        $this->checkVisibility($Product);


        $event = new EventArgs(
            [
                'Product' => $Product,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_FAVORITE_ADD_INITIALIZE);

        if ($this->isGranted('ROLE_USER')) {
            $type = $request->get('type') ?? \Eccube\Entity\CustomerFavoriteProduct::TYPE_FAVORITE;
            $productDetail = $request->get('product_detail_data');
            if (!$productDetail) {
                $productDetail = json_encode([
                    'product_class_id' => 1,
                    'quantity' => 1,
                    'option_id' => 1,
                    'product_option_items' => 1
                ]);
            }
            $Customer = $this->getUser();
            $CustomerFavoriteProduct = $this->customerFavoriteProductRepository->findOneBy(['Customer' => $Customer, 'Product' => $Product, 'type' => $type]);

            if (!$CustomerFavoriteProduct) {
                $this->customerFavoriteProductRepository->addFavorite(
                    $Customer,
                    $Product,
                    $type,
                    $productDetail
                );
                $this->session->getFlashBag()->set('product_detail.just_added_favorite', $Product->getId());
                $action = 'add_favorite';
            } elseif ($request->get(Constant::TOKEN_NAME) && $this->isTokenValid()) {
                $this->customerFavoriteProductRepository->delete($CustomerFavoriteProduct);
                $action = 'delete_favorite';
            }

            $event = new EventArgs(
                [
                    'Product' => $Product,
                ],
                $request
            );
            $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_FAVORITE_ADD_COMPLETE);

            if ($request->isXmlHttpRequest()) {
                return $this->json([
                    'status' => 'success',
                    'action' => $action,
                    'product_id' => $Product->getId(),
                ]);
            }

            return $this->redirectToRoute('product_detail', ['id' => $Product->getId()]);
        } else {
            // 非会員の場合、ログイン画面を表示
            //  ログイン後の画面遷移先を設定
            $this->setLoginTargetPath($this->generateUrl('product_add_favorite', ['id' => $Product->getId()], UrlGeneratorInterface::ABSOLUTE_URL));
            $this->session->getFlashBag()->set('eccube.add.favorite', true);

            $event = new EventArgs(
                [
                    'Product' => $Product,
                ],
                $request
            );
            $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_FAVORITE_ADD_COMPLETE);

            if ($request->isXmlHttpRequest()) {
                return $this->json([
                    'status' => 'error',
                    'redirect_url' => $this->generateUrl('mypage_login'),
                ]);
            }

            return $this->redirectToRoute('mypage_login');
        }
    }

    /**
     * カートに追加.
     *
     * @Route("/products/add_cart/{id}", name="product_add_cart", methods={"POST"}, requirements={"id" = "\d+"})
     */
    public function addCart(Request $request, Product $Product)
    {
        // エラーメッセージの配列
        $errorMessages = [];
        if (!$this->checkVisibility($Product)) {
            throw new NotFoundHttpException();
        }

        $builder = $this->formFactory->createNamedBuilder(
            '',
            AddCartType::class,
            null,
            [
                'product' => $Product,
                'id_add_product_id' => false,
                'allow_extra_fields' => true,
            ]
        );

        $event = new EventArgs(
            [
                'builder' => $builder,
                'Product' => $Product,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_CART_ADD_INITIALIZE);

        /* @var $form \Symfony\Component\Form\FormInterface */
        $form = $builder->getForm();
        $form->handleRequest($request);

        if (!$form->isValid()) {
            throw new NotFoundHttpException();
        }

        $addCartData = $form->getData();

        log_info(
            'カート追加処理開始',
            [
                'product_id' => $Product->getId(),
                'product_class_id' => $addCartData['product_class_id'],
                'quantity' => $addCartData['quantity'],
            ]
        );

        $productOptionItems = $Product->getProductOption()
            ? (array) $request->request->get('product_option_items', [])
            : [];

        // カートへ追加
        $this->cartService->addProduct($addCartData['product_class_id'], $addCartData['quantity'], $productOptionItems);
        // ギフトオプションも追加
        $giftId = $form->has('gift_id') ? $form->get('gift_id')->getData() : null;
        if (!empty($giftId)) {
            $this->cartService->addProduct($giftId, 1);
        }

        // 明細の正規化
        $Carts = $this->cartService->getCarts();
        foreach ($Carts as $Cart) {
            $result = $this->purchaseFlow->validate($Cart, new PurchaseContext($Cart, $this->getUser()));
            // 復旧不可のエラーが発生した場合は追加した明細を削除.
            if ($result->hasError()) {
                $this->cartService->removeProduct($addCartData['product_class_id']);
                foreach ($result->getErrors() as $error) {
                    $errorMessages[] = $error->getMessage();
                }
            }
            foreach ($result->getWarning() as $warning) {
                $errorMessages[] = $warning->getMessage();
            }
        }

        $this->cartService->save();

        log_info(
            'カート追加処理完了',
            [
                'product_id' => $Product->getId(),
                'product_class_id' => $addCartData['product_class_id'],
                'quantity' => $addCartData['quantity'],
            ]
        );

        $event = new EventArgs(
            [
                'form' => $form,
                'Product' => $Product,
            ],
            $request
        );
        $this->eventDispatcher->dispatch($event, EccubeEvents::FRONT_PRODUCT_CART_ADD_COMPLETE);

        if ($event->getResponse() !== null) {
            return $event->getResponse();
        }

        if ($request->isXmlHttpRequest()) {
            // ajaxでのリクエストの場合は結果をjson形式で返す。

            // 初期化
            $messages = [];

            if (empty($errorMessages)) {
                // エラーが発生していない場合
                $done = true;
                array_push($messages, trans('front.product.add_cart_complete'));
            } else {
                // エラーが発生している場合
                $done = false;
                $messages = $errorMessages;
            }

            return $this->json(['done' => $done, 'messages' => $messages]);
        } else {
            // ajax以外でのリクエストの場合はカート画面へリダイレクト
            foreach ($errorMessages as $errorMessage) {
                $this->addRequestError($errorMessage);
            }

            return $this->redirectToRoute('cart');
        }
    }

    /**
     * ページタイトルの設定
     *
     * @param  array|null $searchData
     *
     * @return str
     */
    protected function getPageTitle($searchData)
    {
        if (isset($searchData['name']) && !empty($searchData['name'])) {
            return trans('front.product.search_result');
        } elseif (isset($searchData['category_id']) && $searchData['category_id']) {
            return $searchData['category_id']->getName();
        } else {
            return trans('front.product.all_products');
        }
    }

    /**
     * 閲覧可能な商品かどうかを判定
     *
     * @param Product $Product
     *
     * @return boolean 閲覧可能な場合はtrue
     */
    protected function checkVisibility(Product $Product)
    {
        $is_admin = $this->session->has('_security_admin');

        // 管理ユーザの場合はステータスやオプションにかかわらず閲覧可能.
        if (!$is_admin) {
            // 在庫なし商品の非表示オプションが有効な場合.
            // if ($this->BaseInfo->isOptionNostockHidden()) {
            //     if (!$Product->getStockFind()) {
            //         return false;
            //     }
            // }
            // 公開ステータスでない商品は表示しない.
            if ($Product->getStatus()->getId() !== ProductStatus::DISPLAY_SHOW) {
                return false;
            }
        }

        return true;
    }

    /**
     *
     * @Route("/products/keyword", name="product_keyword", methods={"GET"})
     * @Template("Product/keyword.twig")
     */
    public function keyword()
    {
        return [];
    }

    /**
     *
     * @Route("/products/recommend/{group?}", name="recommend", methods={"GET"}, requirements={"group" = "^[a-zA-Z0-9,]+$"})
     * @Template("Product/recommend.twig")
     */
    public function recommend()
    {
        return [];
    }

    /**
     * 商品詳細画面.
     *
     * @Route("/products/cart_recommend/", name="cart_recommend", methods={"GET"})
     * @Template("Product/cart_recommend.twig")
     */
    public function cartRecommend(Request $request)
    {
        $productIds = $request->query->get('product_ids');
        $qb = $this->entityManager->createQueryBuilder();

        $qb->select('p')
            ->from(OrderItem::class, 'od1')
            ->innerJoin(OrderItem::class, 'od2', 'WITH', 'od1.Order = od2.Order')
            ->innerJoin(Product::class, 'p', 'WITH', 'od2.Product = p')
            ->innerJoin(Order::class, 'o', 'WITH', 'od1.Order = o')
            ->where('od1.Product IN (:productIds)')
            ->andWhere('od2.Product NOT IN (:productIds)')
            ->andWhere('o.OrderStatus = :orderStatus')
            ->andWhere('o.order_date > :targetDateStart')
            ->andWhere('p.Status = :Disp')
            ->setParameter('productIds', $productIds)
            ->setParameter('orderStatus', OrderStatus::DELIVERED)
            ->setParameter('targetDateStart', Carbon::today()->subYear())
            ->setParameter('Disp', ProductStatus::DISPLAY_SHOW)
            ->groupBy('p.id')
            ->setMaxResults(12);

        $products = $qb->getQuery()->getResult();

        $productCount = 12 - count($products);

        if ($productCount > 0) {
            $qb1 = $this->entityManager->createQueryBuilder();
            $qb1->select('c.id')
                ->from(Product::class, 'prod')
                ->innerJoin(ProductCategory::class, 'pc', 'WITH', 'prod.id = pc.product_id')
                ->innerJoin(Category::class, 'c', 'WITH', 'c.id = pc.category_id')
                ->where('prod.id IN (:productIds)')
                ->andWhere('prod.Status = :Disp')
                ->andWhere('c.Parent IS NULL')
                ->setParameter('Disp', ProductStatus::DISPLAY_SHOW)
                ->setParameter('productIds', $productIds)
                ->groupBy('c.id');

            $category = $qb1->getQuery()->getResult();

            $existingProductIds = array_map(fn($product) => $product->getId(), $products);
            $qb2 = $this->entityManager->createQueryBuilder();

            $qb2->select('pr')
                ->from(Product::class, 'pr')
                ->innerJoin(ProductCategory::class, 'pc', 'WITH', 'pr.id = pc.product_id')
                ->where('pc.category_id IN (:categoryIds)')
                ->andWhere('pr.id NOT IN (:productIds)')
                ->setParameter('categoryIds', $category)
                ->setParameter('productIds', array_merge($productIds, $existingProductIds));

            $qb2->groupBy('pr.id')->setMaxResults($productCount);

            $products = array_merge($products, $qb2->getQuery()->getResult());
        }

        return new Response($this->renderView('Product/cart_recommend.twig', [
            'products' => $products,
        ]));
    }

     /**
     * 商品詳細画面.
     *
     * @Route("/products/cart_buy_later/", name="cart_buy_later", methods={"GET"})
     * @Template("Product/cart_buy_later.twig")
     */
    public function cartBuyLater(Request $request)
    {
        $productIds = $request->query->get('product_ids');
        $Customer = $this->getUser();

        if (!$Customer) {
            return new Response($this->renderView('Product/cart_buy_later.twig', [
                'products' => [],
            ]));
        }

        $qb = $this->customerFavoriteProductRepository->getQueryBuilderByCustomer(
            $Customer,
            \Eccube\Entity\CustomerFavoriteProduct::TYPE_LATER
        );

        // カートに入っている商品を除外
        if ($productIds) {
            $qb->andWhere('p.id NOT IN (:productIds)')
               ->setParameter('productIds', $productIds);
        }

        $qb->setMaxResults($this->eccubeConfig['eccube_search_pmax']);

        $products = $qb->getQuery()->getResult();

        return new Response($this->renderView('Product/cart_buy_later.twig', [
            'products' => $products,
        ]));
    }
}
