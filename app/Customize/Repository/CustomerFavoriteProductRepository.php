<?php

/*
 * This file is part of EC-CUBE
 *
 * Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * http://www.ec-cube.co.jp/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Customize\Repository;

use Doctrine\ORM\QueryBuilder;

/**
 * CustomerFavoriteProductRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class CustomerFavoriteProductRepository extends \Eccube\Repository\CustomerFavoriteProductRepository
{
    /**
     * @param \Eccube\Entity\Customer $Customer
     * @param \Eccube\Entity\Product $Product
     * @param int $type
     * @param string|null $productDetail
     * @param int|null $productClassId
     */
    public function addFavorite(\Eccube\Entity\Customer $Customer, \Eccube\Entity\Product $Product, $type = \Eccube\Entity\CustomerFavoriteProduct::TYPE_FAVORITE, $productDetail = null)
    {
        if ($this->isFavorite($Customer, $Product, $type)) {
            return;
        } else {
            $CustomerFavoriteProduct = new \Eccube\Entity\CustomerFavoriteProduct();
            $CustomerFavoriteProduct->setCustomer($Customer);
            $CustomerFavoriteProduct->setProduct($Product);
            $CustomerFavoriteProduct->setType($type);
            $CustomerFavoriteProduct->setProductDetail($productDetail);
            $em = $this->getEntityManager();
            $em->persist($CustomerFavoriteProduct);
            $em->flush();
        }
    }

    /**
     * @param \Eccube\Entity\Customer $Customer
     * @param \Eccube\Entity\Product $Product
     * @param int $type
     *
     * @return bool
     */
    public function isFavorite(\Eccube\Entity\Customer $Customer, \Eccube\Entity\Product $Product, $type = \Eccube\Entity\CustomerFavoriteProduct::TYPE_FAVORITE)
    {
        $qb = $this->createQueryBuilder('cf')
            ->select('COUNT(cf.Product)')
            ->andWhere('cf.Customer = :Customer AND cf.Product = :Product AND cf.type = :type')
            ->setParameters([
                'Customer' => $Customer,
                'Product' => $Product,
                'type' => $type,
            ]);
        $count = $qb
            ->getQuery()
            ->getSingleScalarResult();

        return $count > 0;
    }


    /**
     * @param \Eccube\Entity\Customer $Customer
     * @param int $type
     *
     * @return QueryBuilder
     */
    public function getQueryBuilderByCustomer(\Eccube\Entity\Customer $Customer, $type = \Eccube\Entity\CustomerFavoriteProduct::TYPE_FAVORITE)
    {
        $qb = $this->createQueryBuilder('cfp')
            ->select('cfp, p')
            ->innerJoin('cfp.Product', 'p')
            ->where('cfp.Customer = :Customer AND p.Status = 1')
            ->andWhere('cfp.type = :type')
            ->setParameter('Customer', $Customer)
            ->setParameter('type', $type);

        // Order By
        $qb->addOrderBy('cfp.create_date', 'DESC');

        return $qb;
    }
}
