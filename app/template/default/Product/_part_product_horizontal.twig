{% block javascript %}
    <script>
        {# $(function() {
            $('.add-cart').on('click', function(event) {
                {% if form.classcategory_id1 is defined %}

                console.log(form);
                // 規格1フォームの必須チェック
                if ($('#classcategory_id1').val() == '__unselected' || $('#classcategory_id1').val() == '') {
                    $('#classcategory_id1')[0].setCustomValidity('{{ '項目が選択されていません'|trans }}');
                    return true;
                } else {
                    $('#classcategory_id1')[0].setCustomValidity('');
                }
                {% endif %}

                {% if form.classcategory_id2 is defined %}
                // 規格2フォームの必須チェック
                if ($('#classcategory_id2').val() == '__unselected' || $('#classcategory_id2').val() == '') {
                    $('#classcategory_id2')[0].setCustomValidity('{{ '項目が選択されていません'|trans }}');
                    return true;
                } else {
                    $('#classcategory_id2')[0].setCustomValidity('');
                }
                {% endif %}

                // 個数フォームのチェック
                if ($('#quantity').val() < 1) {
                    $('#quantity')[0].setCustomValidity('{{ '1以上で入力してください。'|trans }}');
                    return true;
                } else {
                    $('#quantity')[0].setCustomValidity('');
                }

                event.preventDefault();
                $form = $('#form1');
                $.ajax({
                    url: $form.attr('action'),
                    type: $form.attr('method'),
                    data: $form.serialize(),
                    dataType: 'json',
                    beforeSend: function(xhr, settings) {
                        // Buttonを無効にする
                        $('.add-cart').prop('disabled', true);
                    }
                }).done(function(data) {
                    // レスポンス内のメッセージをalertで表示
                    $.each(data.messages, function() {
                        $('#ec-modal-header').text(this);
                    });

                    $('.ec-modal').show()

                    // カートブロックを更新する
                    $.ajax({
                        url: "{{ url('block_cart') }}",
                        type: 'GET',
                        dataType: 'html'
                    }).done(function(html) {
                        $('.ec-headerRole__cart').html(html);
                    });
                }).fail(function(data) {
                    alert('{{ 'カートへの追加に失敗しました。'|trans }}');
                }).always(function(data) {
                    // Buttonを有効にする
                    $('.add-cart').prop('disabled', false);
                });
            });
        });

        $('.ec-modal-wrap').on('click', function(e) {
            // モーダル内の処理は外側にバブリングさせない
            e.stopPropagation();
        });
        $('.ec-modal-overlay, .ec-modal, .ec-modal-close, .ec-inlineBtn--cancel').on('click', function() {
            $('.ec-modal').hide()
        }); #}
    </script>
{% endblock %}
<div class="mx-auto px-[5%] md:px-[0%] w-full max-w-[960px]">
    <div class="mx-auto flex flex-col md:flex-row">
        <a href="{{ url('product_detail', {'id': Product.id}) }}" class="flex-[3] hover:opacity-50 text-left flex flex-col md:flex-row">
            {% set image = asset(Product.mainFileName|no_image_product, 'save_image') %}
            <div class="flex-[1]">
                <img class="object-cover rounded-2xl w-full aspect-square"
                    src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" alt="{{ Product.name }}">
            </div>
            <div class="flex-[3] md:flex-[5] flex flex-col ml-[5%] md:ml-[3%] md:py-2">
                <p class="pt-2 pb-1 text-[13px] md:text-[14px]">{{ Product.name }}</p>
                {% if Product.getPrice01IncTaxMin is not null %}
                    <p>
                        <span class="line-through text-[12px] md:text-[16px]">
                            <span>
                                {% if Product.hasProductClass -%}
                                    {% if Product.getPrice01IncTaxMin == Product.getPrice01IncTaxMax %}
                                        {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                    {% else %}
                                        {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                        <span class="text-[10px] md:text-[12px]">円</span> ～
                                        {{ Product.getPrice01IncTaxMax|number_format(0, '.', ',') }}
                                    {% endif %}
                                {% else %}
                                    {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                {% endif %}
                            </span>
                            <span class="text-[10px] md:text-[12px]">円</span>
                        </span>
                        <span class="line-through text-[8px] md:text-[12px]"> (税込)</span>
                    </p>
                    <p class="text-[red]">
                        <span class="text-[14px] md:text-[18px]">
                            <span>
                                {% if Product.hasProductClass -%}
                                    {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                        {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                    {% else %}
                                        {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                        <span class="text-[12px] md:text-[14px]">円</span> ～
                                        {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                                    {% endif %}
                                {% else %}
                                    {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                {% endif %}
                            </span>
                            <span class="text-[12px] md:text-[14px]">円</span>
                        </span>
                        <span class="text-[10px] md:text-[12px] pr-2"> (税込)</span>
                        {% if Product.stock_find == 0 %}
                            <span class="inline-block text-[11px] md:text-[12px] text-[#FF0000]">
                                Sold out
                            </span>
                        {% endif %}
                    </p>
                {% else %}
                    <p class="text-[14px] md:text-[18px]">
                        {% if Product.hasProductClass -%}
                            {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                            {% else %}
                                {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                <span class="text-[12px] md:text-[14px]">円</span> ～
                                {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                            {% endif %}
                        {% else %}
                            {{Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                        {% endif %}
                        <span class="text-[12px] md:text-[14px]">円</span>
                        <span class="text-[10px] md:text-[12px] pr-2"> (税込)</span>
                        {% if Product.stock_find == 0 %}
                            <span class="inline-block text-[11px] md:text-[12px] text-[#FF0000]">
                                Sold out
                            </span>
                        {% endif %}
                    </p>
                {% endif %}
            </div>
        </a>
        <div class="flex-[1] pt-[40px] md:pt-0 flex flex-col">
            <div class="relative flex mt-[20px] justify-end items-center">
                <p class="mr-[3%] text-[#333333] text-[14px] md:text-[15px]">数量</p>
                <select class="handle_item h-[30px] md:h-auto w-[65px] py-1 px-1 pl-[23px] text-left text-[14px] md:text-[15px] border-[#707070] rounded-full border border-solid form-select bg-white appearance-none" disabled>
                    <option value="1">1</option>
                </select>
            </div>
        </div>
    </div>
    <div class="flex justify-end items-center py-[20px]">
        {# <form action="{{ url('product_add_cart', {id:Product.id}) }}" method="post" id="form1" name="form1">
            {% if Product.products_later %}
                <div class="ec-productRole__actions">
                    {% if form.classcategory_id1 is defined %}
                        <div class="ec-select">
                            {{ form_row(form.classcategory_id1) }}
                            {{ form_errors(form.classcategory_id1) }}
                        </div>
                        {% if form.classcategory_id2 is defined %}
                            <div class="ec-select">
                                {{ form_row(form.classcategory_id2) }}
                                {{ form_errors(form.classcategory_id2) }}
                            </div>
                        {% endif %}
                    {% endif %}
                    <div class="ec-numberInput"><span>{{ '数量'|trans }}</span>
                        {{ form_widget(form.quantity) }}
                        {{ form_errors(form.quantity) }}
                    </div>
                </div>
                <div class="ec-productRole__btn">
                    <button type="submit" class="ec-blockBtn--action add-cart">
                        bbb bbb
                    </button>
                </div>
            {% else %}
                <div class="ec-productRole__btn">
                    <button type="button" class="ec-blockBtn--action" disabled="disabled">
                        aaa aaaa
                    </button>
                </div>

            {{ form_rest(form) }}
        </form> #}
        <a class="flex items-center w-full justify-end max-w-[110px] h-[30px] md:max-w-[68px] md:h-[34px]" href="{{ url('cart_buy_later_delete', { id : Product.id, type: constant('Eccube\\Entity\\CustomerFavoriteProduct::TYPE_LATER') }) }}" {{ csrf_token_for_anchor() }} data-method="delete">
            <div class="text-[11px] md:text-[14px] w-full h-full items-center hover:opacity-50 flex">
                <button class="text-[#333333] rounded font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 ml-3 underline underline-offset-2 items-center" type="submit">消去</button>
            </div>
        </a>
    </div>
    <div class="cart-buy-later"></div>
</div>