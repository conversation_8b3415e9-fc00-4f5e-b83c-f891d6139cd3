{#
This file is part of EC-CUBE

Copyright(c) EC-CUBE CO.,LTD. All Rights Reserved.

http://www.ec-cube.co.jp/

For the full copyright and license information, please view the LICENSE
file that was distributed with this source code.
#}
{% extends 'default_frame.twig' %}

{% set body_class = 'cart_page' %}
{% set isAlcohol = false %}
{% set product_ids = [] %}

{% block javascript %}
    <script>
        $(function() {
            $('.handle_item').on('change',function(e) {
                let changeLink = $(this).prev();
                changeLink
                    .attr('href', changeLink.data('href') + '?quantity=' + ($(this).val() - changeLink.data('quantity')))
                    .trigger('click');
            });

            $('[data-gift-class-id]').each(function() {
                let removeLink = $(this).closest('li').prev().find('a[href*="remove"]');
                removeLink.attr('href', removeLink.attr('href') + '?gift_class_id=' + $(this).data('gift-class-id'));
            });

            $('body').on('click', '#buy-later', function(event) {
                event.preventDefault();
                $.ajax({
                    type: 'POST',
                    url: $(this).data('url'),
                }).always(function (response) {
                    $(this).next('a').trigger('click');
                }.bind(this));
            })
        });
    </script>
{% endblock javascript %}

{% block main %}

<!-- Breadcrumb -->
<div class="container max-w-screen-xl mx-auto px-[5%] md:px-[3%]">
    <div class="bg-white py-[20px] flex items-center flex-wrap">
        <ul class="flex flex-wrap items-center text-[12px] text-[#bababa]">
            <li class="inline-flex items-center">
                <a href="{{ url('homepage') }}" class="hover:opacity-50 cursor-pointer">
                    ホーム
                </a>
                <span class="mx-1">/</span>
            </li>
            <li class="inline-flex items-center">
                カートを見る
            </li>
        </ul>
    </div>
</div>

<!-- CART -->
<div class="mx-auto px-[5%] md:px-[3%] w-full max-w-[960px]">
    <div class="max-w-[670px] md:max-w-[750px] mx-auto">
        {# STEP #}
        <ul class="mx-auto flex justify-between max-w-[640px] md:max-w-[480px] items-center py-[20px] md:py-[40px]">
            {% set step = 1 %}
            <li class="flex flex-col items-center relative w-1/4">
                <div class="tracking-[.06em] din-condensed font-light text-[17px] md:text-[27px]">STEP{{ step }}{% set step = step + 1 %}</div>
                <div class="rounded-full bg-[#333333] h-[10px] w-[10px] md:h-[14px] md:w-[14px] z-[100]"></div>
                <p class="absolute -bottom-8 md:-bottom-12 w-full flex-wrap text-center justify-center text-[10px] md:text-[12px]">カートを<br>見る</p>
            </li>
            {% if is_granted('ROLE_USER') == false %}
            <li class="flex flex-col items-center relative w-1/4 text-[#bababa]">
                <div class="tracking-[.06em] din-condensed font-light text-[17px] md:text-[27px]">STEP{{ step }}{% set step = step + 1 %}</div>
                <div class="rounded-full bg-[#bababa] h-[10px] w-[10px] md:h-[14px] md:w-[14px] z-[100]"></div>
                <div class="absolute right-[50%] top-[1.9rem] md:top-12 bg-[#bababa] h-[1px] w-full z-10"></div>
                <p class="absolute -bottom-8 md:-bottom-12 w-full flex-wrap text-center text-[10px] md:text-[12px]">お客様情報<br>&nbsp;</p>
            </li>
            {% endif %}
            <li class="flex flex-col items-center relative w-1/4 text-[#bababa]">
                <div class="tracking-[.06em] din-condensed font-light text-[17px] md:text-[27px]">STEP{{ step }}{% set step = step + 1 %}</div>
                <div class="rounded-full bg-[#bababa] h-[10px] w-[10px] md:h-[14px] md:w-[14px] z-[100]"></div>
                <div class="absolute right-[50%] top-[1.9rem] md:top-12 bg-[#bababa] h-[1px] w-full z-10"></div>
                <div class="absolute -bottom-8 md:-bottom-12 w-full flex-wrap text-center text-[10px] md:text-[12px]">ご注文手続き<br>&nbsp;</div>
            </li>
            <li class="flex flex-col items-center relative w-1/4 text-[#bababa]">
                <div class="tracking-[.06em] din-condensed font-light text-[17px] md:text-[27px]">STEP{{ step }}{% set step = step + 1 %}</div>
                <div class="rounded-full bg-[#bababa] h-[10px] w-[10px] md:h-[14px] md:w-[14px]"></div>
                <div class="absolute right-[50%] top-[1.9rem] md:top-12 bg-[#bababa] h-[1px] w-full z-10"></div>
                <div class="absolute -bottom-8 md:-bottom-12 w-full flex-wrap text-center text-[10px] md:text-[12px]">ご注文内容の<br>確認</div>
            </li>
            <li class="flex flex-col items-center relative w-1/4 text-[#bababa]">
                <div class="tracking-[.06em] din-condensed font-light text-[17px] md:text-[27px]">STEP{{ step }}{% set step = step + 1 %}</div>
                <div class="rounded-full bg-[#bababa] h-[10px] w-[10px] md:h-[14px] md:w-[14px]"></div>
                <div class="absolute right-[50%] top-[1.9rem] md:top-12 bg-[#bababa] h-[1px] w-full z-10"></div>
                <div class="absolute -bottom-8 md:-bottom-12 w-full flex-wrap text-center text-[10px] md:text-[12px]">完了<br>&nbsp;</div>
            </li>
        </ul>
        <!-- Title -->
        <div class="flex flex-col font-semibold items-center pt-[40px] md:pt-[60px]">
            <p class="text-[10px] md:text-[16px] tracking-[.06em] font-semibold">カートを見る</p>
            <div class="text-[30px] md:text-[46px] tracking-[.06em] font-light din-condensed text-center">
                <p>CART</p>
                <div class="border-b-2 border-[#fee44d] w-full mx-auto"></div>
            </div>
        </div>

        {% for error in app.session.flashbag.get('eccube.front.cart.error') %}
            <div class="mt-[20px] text-[red]">
                {{ error|trans|nl2br }}
            </div>
        {% endfor %}

        {% if totalQuantity > 0 %}
            {% if Carts|length > 1 %}
                <div class="ec-cartRole__error pt-[40px] md:pt-[60px]">
                    <div class="ec-alert-warning">
                        <div class="ec-alert-warning__text text-[13px] md:text-[14px]">
                            {{ '同時購入できない商品がカートに含まれています。'|trans|nl2br }}
                        </div>
                    </div>
                </div>
            {% endif %}
            <form name="form" id="form_cart" class="ec-cartRole" method="post" action="{{ url('cart') }}">
                {% for CartIndex,Cart in Carts %}
                    {% set isAlcohol = false %}
                    {% set cartKey = Cart.cart_key %}

                    {% for error in app.session.flashbag.get('eccube.front.cart.' ~ cartKey ~ '.request.error') %}
                        <div class="mt-[20px] text-[red]">
                            {{ error|trans|nl2br }}
                        </div>
                    {% endfor %}

                    {% set discountGroupCategory = app.session.get('discountGroupCategory.' ~ cartKey, []) %}
                    {% set discountGroupCategoryTotal = 0 %}

                    <!-- List item -->
                    <ul class="flex flex-wrap gap-x-[4%] m-auto pt-[40px]">
                        {% set has_product_with_gift = false %}
                        {% set last_product_class_id = false %}
                        {% for CartItem in Cart.CartItems %}
                            {% set ProductClass = CartItem.ProductClass %}
                            {% set Product = ProductClass.Product %}
                            {% set product_ids = product_ids|merge([Product.id]) %}

                            {% if Product.Tags is not empty %}
                                {% for Tag in Product.Tags %}
                                    {% if Tag.id == eccube_config['alcohol_product_tag_id'] %}
                                        {% set isAlcohol = true %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}

                            {% if Product.gift_type > 0 %}
                                {% set has_product_with_gift = true %}
                            {% endif %}
                            <li class="flex flex-col w-full">
                                {% if Product.gift_type != constant('Eccube\\Entity\\Product::GIFT_OPTIONS') and loop.first == false %}
                                    <!-- Hr -->
                                    <div class="max-w-[960px] w-full mx-auto py-2 lg:px-0">
                                        <div class="bg-[#bababa] h-[1px]"></div>
                                    </div>
                                {% endif %}

                                <div class="flex flex-col md:flex-row">
                                {% if Product.gift_type != constant('Eccube\\Entity\\Product::GIFT_OPTIONS') %}
                                    <div class="flex flex-row md:w-[68%]">
                                        <div class="flex-[1]">
                                            <a target="_blank" href="{{ url('product_detail', {id : Product.id} ) }}">
                                                {% set image = asset(Product.MainListImage|no_image_product, 'save_image') %}
                                                <img class="object-cover rounded-2xl w-full aspect-square" src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" alt="{{ Product.name }}">
                                            </a>
                                        </div>
                                        <div class="flex-[3] md:flex-[5] flex flex-col ml-[5%] md:ml-[3%] md:py-2">
                                            <a class="font-semibold tracking-[.06em] text-[13px] md:text-[14px]"
                                                target="_blank" href="{{ url('product_detail', {id : Product.id} ) }}">{{ Product.name }}{% if CartItem.productOptionItems %}　( {{ CartItem.productOptionItems }} ){% endif %}</a>
                                            <div>
                                                {% if ProductClass.ClassCategory1 and ProductClass.ClassCategory1.id %}
                                                    <p class="text-[11px] md:text-[12px]">
                                                        {{ ProductClass.ClassCategory1.ClassName.name }} {% if ProductClass.ClassCategory2 %} / {{ ProductClass.ClassCategory2.ClassName.name }}{% endif %}
                                                        ：{{ ProductClass.ClassCategory1 }}{% if ProductClass.ClassCategory2 %} / {{ ProductClass.ClassCategory2 }}{% endif %}
                                                    </p>
                                                {% endif %}

                                                {% set priceWithDiscount = CartItem.price + (discountGroupCategory[ProductClass.id] ?? 0) %}

                                                <p class="text-[16px] md:text-[18px]">{{ priceWithDiscount|number_format(0, '.', ',') }}
                                                    <span class="font-semibold">円</span>
                                                    <span class="text-[11px] md:text-[12px]">(<span class="font-semibold">税込</span>)</span>
                                                </p>
                                                {% if priceWithDiscount != CartItem.price %}
                                                    <p class="text-[11px] md:text-[12px] text-[red]">
                                                        (まとめ買い値引 {{ discountGroupCategory[ProductClass.id]|number_format(0, '.', ',') }}円×{{ CartItem.quantity }})
                                                        {% set discountGroupCategoryTotal = discountGroupCategoryTotal + (discountGroupCategory[ProductClass.id] * CartItem.quantity) %}
                                                    </p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="md:w-[32%] pt-[40px] md:pt-0 flex flex-col">
                                        <div class="relative flex mt-[20px] justify-end items-center">
                                            <p class="mr-[3%] text-[#333333] text-[14px] md:text-[15px]">数量</p>
                                            <a data-quantity="{{ CartItem.quantity }}" data-href="{{ url('cart_handle_item', {'operation': 'up', 'productClassId': ProductClass.id}) }}" {{ csrf_token_for_anchor() }} class="hidden" data-method="put" data-confirm="false"></a>
                                            <select class="handle_item  h-[30px] md:h-auto w-[65px] py-1 px-1 pl-[23px] text-left text-[14px] md:text-[15px] border-[#707070] rounded-full border border-solid form-select bg-white appearance-none">
                                                {% for i in 1..CartItem.quantity + 10 %}
                                                    <option value="{{ i }}" {{ i == CartItem.quantity ? 'selected' : '' }}>{{ i }}</option>
                                                {% endfor %}
                                            </select>
                                            <svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-600 pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="flex flex-row md:w-[68%] ml-[40px]">
                                        <div class="flex-[1]">
                                            {% set image = asset(Product.MainListImage|no_image_product, 'save_image') %}
                                            <img class="object-cover rounded-2xl w-full aspect-square" src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" alt="{{ Product.name }}">
                                        </div>
                                        <div class="flex-[3] md:flex-[5] flex flex-col ml-[5%] md:ml-[3%] md:py-2">
                                            <p class="font-semibold tracking-[.06em] text-[13px] md:text-[14px]">{{ Product.name }}</p>
                                            <div>
                                                {% if ProductClass.ClassCategory1 and ProductClass.ClassCategory1.id %}
                                                    <p class="text-[11px] md:text-[12px]">
                                                        {{ ProductClass.ClassCategory1.ClassName.name }} {% if ProductClass.ClassCategory2 %} / {{ ProductClass.ClassCategory2.ClassName.name }}{% endif %}
                                                        ：{{ ProductClass.ClassCategory1 }}{% if ProductClass.ClassCategory2 %} / {{ ProductClass.ClassCategory2 }}{% endif %}
                                                    </p>
                                                {% endif %}
                                                <p class="text-[16px] md:text-[18px]">{{ CartItem.price|number_format(0, '.', ',') }}
                                                    <span class="font-semibold">円</span>
                                                    <span class="text-[11px] md:text-[12px]">(<span class="font-semibold">税込</span>)</span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="md:w-[32%] pt-[40px] md:pt-0 flex flex-row justify-end">
                                        <div class="flex justify-start items-center">
                                            <a href="{{ path('cart_gift') }}" class="bg-[#bababa] text-[#fff] rounded font-semibold items-center justify-center text-center flex tracking-[.06em] text-[11px] md:text-[14px] hover:opacity-50 w-[110px] h-[30px] md:w-[68px] md:h-[34px] ml-4">変更</a>
                                        </div>
                                    </div>
                                {% endif %}
                                </div>

                                {% set last_product_class_id = ProductClass.id %}
                                {% if Product.gift_type != constant('Eccube\\Entity\\Product::GIFT_OPTIONS') %}
                                    <div class="flex justify-end items-center pt-[20px]">
                                        <p class="pr-8 text-[13px] md:text-[14px] font-semibold">小計</p>
                                        <p class="text-[16px] md:text-[18px]">{{ (priceWithDiscount * CartItem.quantity)|number_format(0, '.', ',') }}
                                            <span class="font-semibold">円</span>
                                        </p>
                                    </div>
                                {% endif %}
                                <div class="flex justify-end items-center py-[20px]">
                                    {% set product_detail_data = {
                                        'product_class_id': ProductClass.id,
                                        'quantity': CartItem.quantity,
                                        'option_id': Product.ProductOption ? Product.ProductOption.id : null,
                                        'product_option_items': CartItem.productOptionItems
                                    } %}
                                    <p id="buy-later" data-url="{{ url('product_add_favorite', {'id':Product.id, 'type': constant('Eccube\\Entity\\CustomerFavoriteProduct::TYPE_LATER'), 'product_detail_data': product_detail_data|json_encode }) }}" data-message="カートから商品を削除してもよろしいですか?"
                                        class="cursor-pointer text-[#333333] font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 mr-3 underline underline-offset-2">あとで買う</p> |
                                    <a href="{{ url('cart_handle_item', {'operation': 'remove', 'productClassId': ProductClass.id }) }}" {{ csrf_token_for_anchor() }} data-method="put" data-message="カートから商品を削除してもよろしいですか?"
                                    class="text-[#333333] rounded font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 ml-3 underline underline-offset-2">削除</a>
                                </div>
                            </li>

                        {% endfor %}
                    </ul>

                    <!-- Hr -->
                    <div class="max-w-[960px] w-full mx-auto py-2 lg:px-0">
                        <div class="bg-[#bababa] h-[1px]"></div>
                    </div>

                    <div class="flex justify-end items-end text-[13px] md:text-[14px]">
                        <p class="pr-8 font-semibold pb-2">ご注文金額合計</p>
                        <p class="text-[24px] md:text-[32px]">{{  (Cart.totalPrice + discountGroupCategoryTotal)|number_format(0, '.', ',') }}
                            <span class="text-[18px] md:text-[24px] font-semibold">円</span>
                        </p>
                    </div>

                    <!-- Hr -->
                    <div class="max-w-[960px] w-full mx-auto py-2 lg:px-0">
                        <div class="bg-[#bababa] h-[1px]"></div>
                    </div>
                    <div class="py-10">
                        {% if has_product_with_gift %}
                            <div class="flex flex-col md:flex-row justify-end items-center">
                                <a href="{{ path('cart_gift') }}" class="w-full h-[50px] justify-center items-center text-center flex border-[#333333] border border-solid rounded text-[#333333] bg-[#fff] text-[14px] md:text-[16px]">カート内商品の一括ギフト設定</a>
                            </div>
                        {% endif %}

                        {% if isAlcohol %}
                            <div class="mt-[30px] border border-black grid gap-2">
                                <div class="px-6 py-4">
                                    <p class="md:text-[24px] text-[18px] font-bold">ご購入前に必ずご確認ください</p>
                                    <p class="md:text-[18px] text-[15px] font-bold">酒類の販売について</p>
                                    <ul class="md:text-[14px] text-[13px] list-disc pl-6">
                                        <li>法律により20歳未満の酒類の購入や飲酒は禁止されています。また酒類の販売には年齢確認が義務付けられています。</li>
                                        <li>20歳未満の場合はご購入いただけません。「<a href="/guide#alcohol" class="text-[#0033FF]">ご利用ガイド</a>を確認し、注意事項を理解しました」をチェックして、ご購入手続きへ進まれた場合は、20歳以上であることに同意いただいたことになります。</li>
                                    </ul>
                                </div>
                                <div class="md:text-[14px] text-[13px] px-6 py-4 bg-[#F7F7F7]">
                                    <label class="ml-2">
                                        <input type="checkbox" class="mr-1" onchange="$('.proceed-purchase{{ CartIndex }}').toggleClass('pointer-events-none bg-[#BABABA]', !this.checked).toggleClass('bg-[#a6011a]', this.checked)">
                                        注意事項を理解しました
                                    </label>
                                </div>
                            </div>
                        {% endif %}

                        {% set cartKeyParts = cartKey|split('_') %}
                        {% set isSolarType = cartKeyParts[1] is defined and cartKeyParts[1] == eccube_config['sale_type_solar'] %}

                        {% if isSolarType %}
                            <div class="flex flex-col md:flex-row justify-end items-center">
                                <p class="w-full h-[70px] justify-center items-center text-center flex border-[#333333] border border-solid rounded text-[#333333] bg-[#fff] text-[14px] md:text-[16px]">
                                ご注文後はご担当者よりご連絡いたします。<br>お申込みによりご注文が確定するわけではございません。
                                </p>
                            </div>
                        {% endif %}
                        <div class="flex flex-col items-center gap-[10px] md:gap-[20px] md:pt-8 pt-5">
                            <a href="{{ path('cart_buystep', {'cart_key': cartKey}) }}" class="{{ isAlcohol ? 'proceed-purchase' ~ CartIndex ~ ' pointer-events-none bg-[#BABABA]' : 'bg-[#a6011a]' }} w-full md:w-[50%] h-[50px] justify-center items-center text-center flex rounded text-[#fff] text-[17px] md:text-[16px]">
                                {{ isSolarType ? 'お申し込みへ進む' : '購入へ進む' }}
                            </a>
                            {% if loop.last %}
                                <a href="{{ path('homepage') }}" class="w-fit h-[50px] justify-center items-center text-center flex text-[14px] md:text-[16px] underline underline-offset-2">ショッピングを続ける</a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
                <div class="cart-buy-later"></div>
                <div class="max-w-[960px] w-full mx-auto pb-10">
                    <div class="bg-[#bababa] h-[1px]"></div>
                </div>
                <div class="cart-recommend"></div>
            </form>
        {% else %}
            {% for CartIndex,Cart in Carts %}
                {% set cartKey = Cart.cart_key %}
                {% for error in app.session.flashbag.get('eccube.front.cart.' ~ cartKey ~ '.request.error') %}
                    <div class="mt-[20px] text-[red]">
                        {{ error|trans|nl2br }}
                    </div>
                {% endfor %}
            {% endfor %}
            <div class="text-center py-10">
                {{ '現在カート内に商品はございません。'|trans }}
            </div>
        {% endif %}
    </div>
</div>

<script>
    $(document).ready(function() {
        var productIds = {{ product_ids|json_encode() }};
        if (productIds && productIds.length > 0) {
            $.ajax({
                url: '{{ url('cart_recommend') }}',
                type: 'GET',
                dataType: 'html',
                data: { product_ids: productIds },
            }).done(function(html) {
                $('.cart-recommend').html(html);
            });
        }

        $.ajax({
            url: '{{ url('cart_buy_later') }}',
            type: 'GET',
            dataType: 'html',
            data: { product_ids: productIds },
        }).done(function(html) {
            $('.cart-buy-later').html(html);
        });
    })
</script>
{% endblock %}
